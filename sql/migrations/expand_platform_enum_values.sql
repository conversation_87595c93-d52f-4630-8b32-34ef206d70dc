-- 扩展平台枚举值以支持更多平台
-- 执行时间: 2025-07-31
-- 描述: 为达人提报系统添加更多平台支持（淘宝、快手、B站、微信&视频号、微博、知乎、其他、置换）

USE daren_db;

-- 检查当前数据库连接
SELECT 'Connected to database' AS status, DATABASE() AS current_db;

-- 备份提醒
SELECT '⚠️  建议在执行前备份数据库' AS warning;

-- 检查当前平台枚举值
SELECT 
  TABLE_NAME,
  COLUMN_NAME,
  COLUMN_TYPE,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'daren_db' 
  AND COLUMN_NAME = 'platform'
  AND TABLE_NAME IN ('influencer_reports', 'my_influencers', 'public_influencers', 'crawler_cookies');

-- 1. 更新 influencer_reports 表的平台枚举值
ALTER TABLE influencer_reports 
MODIFY COLUMN platform ENUM(
  'xiaohongshu', 
  'juxingtu', 
  '淘宝', 
  '快手', 
  'B站', 
  '微信&视频号', 
  '微博', 
  '知乎', 
  '其他', 
  '置换'
) NOT NULL COMMENT '平台类型';

-- 2. 更新 my_influencers 表的平台枚举值
ALTER TABLE my_influencers 
MODIFY COLUMN platform ENUM(
  'xiaohongshu', 
  'juxingtu', 
  '淘宝', 
  '快手', 
  'B站', 
  '微信&视频号', 
  '微博', 
  '知乎', 
  '其他', 
  '置换'
) NOT NULL COMMENT '平台类型';

-- 3. 更新 public_influencers 表的平台枚举值
ALTER TABLE public_influencers 
MODIFY COLUMN platform ENUM(
  'xiaohongshu', 
  'juxingtu', 
  '淘宝', 
  '快手', 
  'B站', 
  '微信&视频号', 
  '微博', 
  '知乎', 
  '其他', 
  '置换'
) NOT NULL COMMENT '平台类型';

-- 4. 更新 crawler_cookies 表的平台枚举值（保持原有限制，因为爬虫只支持小红书和巨量星图）
-- 注意：Cookie管理暂时只支持小红书和巨量星图，所以不修改此表
-- ALTER TABLE crawler_cookies 
-- MODIFY COLUMN platform ENUM('xiaohongshu', 'juxingtu') NOT NULL COMMENT '平台类型';

-- 验证修改结果
SELECT 'Platform enum values updated successfully' AS status;

-- 检查修改后的枚举值
SELECT 
  TABLE_NAME,
  COLUMN_NAME,
  COLUMN_TYPE,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'daren_db' 
  AND COLUMN_NAME = 'platform'
  AND TABLE_NAME IN ('influencer_reports', 'my_influencers', 'public_influencers', 'crawler_cookies')
ORDER BY TABLE_NAME;

-- 检查现有数据是否有冲突
SELECT 
  'influencer_reports' AS table_name,
  platform,
  COUNT(*) AS count
FROM influencer_reports 
GROUP BY platform
UNION ALL
SELECT 
  'my_influencers' AS table_name,
  platform,
  COUNT(*) AS count
FROM my_influencers 
GROUP BY platform
UNION ALL
SELECT 
  'public_influencers' AS table_name,
  platform,
  COUNT(*) AS count
FROM public_influencers 
GROUP BY platform
ORDER BY table_name, platform;

SELECT '✅ 平台枚举值扩展完成' AS final_status;
